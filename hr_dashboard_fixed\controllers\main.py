# -*- coding: utf-8 -*-

import logging
from odoo import http
from odoo.http import request

_logger = logging.getLogger(__name__)

class HrDashboard(http.Controller):

    @http.route('/hr_dashboard_fixed/dashboard_data', type='json', auth='user')
    def dashboard_data(self):
        """
        Return the dashboard data
        """
        try:
            _logger.info("Dashboard data requested")
            dashboard = request.env['hr.dashboard'].sudo()
            data = dashboard.get_dashboard_data()
            _logger.info("Dashboard data sent: %s", data)
            return data
        except Exception as e:
            _logger.error("Error in dashboard_data controller: %s", e)
            return {
                'employee_count': 0,
                'department_count': 0,
                'employees_per_department': [],
                'department_statistics': [],
                'new_employees': 0,
                'employees_by_gender': {
                    'male': 0,
                    'female': 0,
                    'other': 0
                },
                'error': str(e)
            }

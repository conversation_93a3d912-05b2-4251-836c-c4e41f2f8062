# -*- coding: utf-8 -*-

import logging
from odoo import models, fields, api, _
from datetime import datetime, timedelta

_logger = logging.getLogger(__name__)


class HrDashboard(models.Model):
    _name = 'hr.dashboard'
    _description = 'HR Dashboard'

    name = fields.Char(string='Name')

    @api.model
    def get_employee_count(self):
        """
        Get the total number of employees
        """
        try:
            employee_count = self.env['hr.employee'].search_count([])
            _logger.info("Employee count: %s", employee_count)
            return employee_count
        except Exception as e:
            _logger.error("Error getting employee count: %s", e)
            return 0

    @api.model
    def get_department_count(self):
        """
        Get the total number of departments
        """
        try:
            department_count = self.env['hr.department'].search_count([])
            _logger.info("Department count: %s", department_count)
            return department_count
        except Exception as e:
            _logger.error("Error getting department count: %s", e)
            return 0

    @api.model
    def get_employees_per_department(self):
        """
        Get the number of employees per department
        """
        try:
            # Get all departments
            departments = self.env['hr.department'].search([])

            # Get employee count per department using read_group
            emp_data = self.env['hr.employee'].read_group(
                [('department_id', 'in', departments.ids)],
                ['department_id'],
                ['department_id']
            )

            # Create a dictionary with department_id as key and count as value
            dept_employee_count = {
                data['department_id'][0]: data['department_id_count']
                for data in emp_data if data['department_id']
            }

            # Total employee count for percentage calculation
            total_employees = sum(dept_employee_count.values())

            # Create result list
            result = []
            for department in departments:
                # Get employee count for this department
                employee_count = dept_employee_count.get(department.id, 0)

                result.append({
                    'id': department.id,
                    'name': department.name,
                    'employee_count': employee_count,
                    'color': self._get_random_color(department.id),
                })

            _logger.info("Employees per department: %s", result)
            _logger.info("Total employees: %s", total_employees)

            return result
        except Exception as e:
            _logger.error("Error getting employees per department: %s", e)
            return []

    @api.model
    def get_dashboard_data(self):
        """
        Get all dashboard data
        """
        try:
            result = {
                'employee_count': self.get_employee_count(),
                'department_count': self.get_department_count(),
                'employees_per_department': self.get_employees_per_department(),
                'new_employees': self.get_new_employees(),
                'employees_by_gender': self.get_employees_by_gender(),
                'department_statistics': self.get_department_statistics(),
            }
            # Log the result for debugging
            _logger.info("Dashboard data: %s", result)
            return result
        except Exception as e:
            # Log the error and return empty data
            _logger.error("Error getting dashboard data: %s", e)
            return {
                'employee_count': 0,
                'department_count': 0,
                'employees_per_department': [],
                'new_employees': 0,
                'employees_by_gender': {
                    'male': 0,
                    'female': 0,
                    'other': 0
                },
                'department_statistics': [],
                'error': str(e)
            }

    @api.model
    def get_new_employees(self):
        """
        Get employees hired in the last 30 days
        """
        try:
            date_from = fields.Date.today() - timedelta(days=30)
            new_employees = self.env['hr.employee'].search_count([
                ('create_date', '>=', date_from)
            ])
            _logger.info("New employees: %s", new_employees)
            return new_employees
        except Exception as e:
            _logger.error("Error getting new employees: %s", e)
            return 0

    @api.model
    def get_employees_by_gender(self):
        """
        Get employees count by gender
        """
        try:
            male_count = self.env['hr.employee'].search_count([('gender', '=', 'male')])
            female_count = self.env['hr.employee'].search_count([('gender', '=', 'female')])
            other_count = self.env['hr.employee'].search_count([('gender', '=', 'other')])

            result = {
                'male': male_count,
                'female': female_count,
                'other': other_count
            }
            _logger.info("Employees by gender: %s", result)
            return result
        except Exception as e:
            _logger.error("Error getting employees by gender: %s", e)
            return {
                'male': 0,
                'female': 0,
                'other': 0
            }

    def _get_random_color(self, seed):
        """
        Generate a random color based on the seed
        """
        colors = [
            '#007bff',  # primary blue
            '#28a745',  # success green
            '#dc3545',  # danger red
            '#ffc107',  # warning yellow
            '#17a2b8',  # info cyan
            '#6f42c1',  # purple
            '#e83e8c',  # pink
            '#fd7e14',  # orange
            '#20c997',  # teal
            '#6c757d',  # gray
            '#343a40',  # dark
            '#f8f9fa'   # light
        ]
        return colors[seed % len(colors)]

    @api.model
    def get_department_statistics(self):
        """
        Get detailed statistics for each department
        """
        try:
            departments = self.env['hr.department'].search([])
            statistics = []

            for dept in departments:
                employees = self.env['hr.employee'].search([('department_id', '=', dept.id)])

                # Calculate statistics
                total_employees = len(employees)
                male_count = len(employees.filtered(lambda e: e.gender == 'male'))
                female_count = len(employees.filtered(lambda e: e.gender == 'female'))

                # Calculate average age if birthday is available
                avg_age = 0
                if employees.filtered('birthday'):
                    ages = []
                    for emp in employees.filtered('birthday'):
                        today = fields.Date.today()
                        age = today.year - emp.birthday.year
                        if today.month < emp.birthday.month or (today.month == emp.birthday.month and today.day < emp.birthday.day):
                            age -= 1
                        ages.append(age)
                    avg_age = sum(ages) / len(ages) if ages else 0

                statistics.append({
                    'department_id': dept.id,
                    'department_name': dept.name,
                    'total_employees': total_employees,
                    'male_count': male_count,
                    'female_count': female_count,
                    'avg_age': round(avg_age, 1),
                    'color': self._get_random_color(dept.id)
                })

            return statistics
        except Exception as e:
            _logger.error("Error getting department statistics: %s", e)
            return []

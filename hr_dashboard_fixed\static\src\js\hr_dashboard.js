odoo.define('hr_dashboard_fixed.dashboard', function (require) {
    "use strict";

    var AbstractAction = require('web.AbstractAction');
    var core = require('web.core');
    var QWeb = core.qweb;
    var rpc = require('web.rpc');
    var session = require('web.session');
    var field_utils = require('web.field_utils');
    var time = require('web.time');

    // Load the Chart.js library
    var Chart = window.Chart;

    var HrDashboard = AbstractAction.extend({
        template: 'hr_dashboard_fixed.dashboard',
        events: {
            'click .o_hr_dashboard_card_footer a': '_onCardFooterClick',
        },

        /**
         * @override
         */
        init: function (parent, context) {
            this._super(parent, context);
            this.dashboardData = {};
        },

        /**
         * @override
         */
        willStart: function () {
            var self = this;
            console.log("Dashboard willStart - fetching data");

            // First call super to initialize the widget
            return this._super().then(function () {
                // Fetch dashboard data without updating UI (will be done in start)
                return self._fetchDashboardData(false).catch(function(error) {
                    console.error("Error in willStart:", error);
                    // We'll handle the error in the catch block of _fetchDashboardData
                    // Just return a resolved promise to continue initialization
                    return Promise.resolve();
                });
            });
        },

        /**
         * @override
         */
        start: function () {
            var self = this;

            // First call super to initialize the widget
            return this._super().then(function () {
                console.log("Dashboard start - rendering dashboard with data:", self.dashboardData);

                // Show loading message
                self.$el.html('<div class="text-center p-5">' +
                    '<i class="fa fa-spinner fa-spin fa-3x"></i>' +
                    '<p class="mt-2">جاري تحميل لوحة المعلومات...</p>' +
                    '</div>');

                // Check if we have data
                if (self.dashboardData && Object.keys(self.dashboardData).length > 0) {
                    self._renderDashboard();
                } else {
                    console.warn("No dashboard data available in start");
                    // Fetch dashboard data with UI updates
                    return self._fetchDashboardData(true).then(function() {
                        self._renderDashboard();
                    });
                }
            }).guardedCatch(function(error) {
                console.error("Error in dashboard start:", error);
                // Show error message to user
                self.$el.html('<div class="alert alert-danger text-center">' +
                    '<h3><i class="fa fa-exclamation-triangle"></i> خطأ في تحميل لوحة المعلومات</h3>' +
                    '<p>يرجى تحديث الصفحة أو الاتصال بمسؤول النظام.</p>' +
                    '<p>التفاصيل التقنية: ' + (error.message || error) + '</p>' +
                    '</div>');
            });
        },

        /**
         * Fetch the dashboard data from the server
         * @private
         * @param {Boolean} updateUI - Whether to update the UI with loading/error messages
         * @returns {Promise}
         */
        _fetchDashboardData: function (updateUI) {
            var self = this;

            // Default to true if not specified
            updateUI = (updateUI !== false);

            // Show loading message if we should update UI and $el is available
            if (updateUI && this.$el) {
                this.$el.html('<div class="text-center p-5">' +
                    '<i class="fa fa-spinner fa-spin fa-3x"></i>' +
                    '<p class="mt-2">جاري تحميل لوحة المعلومات...</p>' +
                    '</div>');
            }

            return rpc.query({
                route: '/hr_dashboard_fixed/dashboard_data',
                params: {}
            }).then(function (data) {
                console.log("Dashboard data received:", data);

                // Check if data is valid
                if (!data) {
                    console.error("No data received from server");
                    if (updateUI && self.$el) {
                        self.$el.html('<div class="alert alert-danger text-center">' +
                            '<h3><i class="fa fa-exclamation-triangle"></i> خطأ في البيانات</h3>' +
                            '<p>لم يتم استلام أي بيانات من الخادم</p>' +
                            '</div>');
                    }
                    return Promise.reject("No data received");
                }

                // Check if there's an error in the data
                if (data.error) {
                    console.error("Error in dashboard data:", data.error);
                    if (updateUI && self.$el) {
                        self.$el.html('<div class="alert alert-danger text-center">' +
                            '<h3><i class="fa fa-exclamation-triangle"></i> خطأ في البيانات</h3>' +
                            '<p>' + data.error + '</p>' +
                            '</div>');
                    }
                    return Promise.reject(data.error);
                }

                self.dashboardData = data;

                // Set default values if data is empty or undefined
                if (!self.dashboardData.employee_count) self.dashboardData.employee_count = 0;
                if (!self.dashboardData.department_count) self.dashboardData.department_count = 0;
                if (!self.dashboardData.new_employees) self.dashboardData.new_employees = 0;
                if (!self.dashboardData.employees_per_department) self.dashboardData.employees_per_department = [];
                if (!self.dashboardData.employees_by_gender) {
                    self.dashboardData.employees_by_gender = {
                        male: 0,
                        female: 0,
                        other: 0
                    };
                }

                return self.dashboardData;
            }).catch(function(error) {
                console.error("Error fetching dashboard data:", error);

                // Show error message to user if we should update UI and $el is available
                if (updateUI && self.$el) {
                    self.$el.html('<div class="alert alert-danger text-center">' +
                        '<h3><i class="fa fa-exclamation-triangle"></i> خطأ في الاتصال</h3>' +
                        '<p>حدث خطأ أثناء جلب البيانات من الخادم. يرجى المحاولة مرة أخرى لاحقًا.</p>' +
                        '<p>التفاصيل التقنية: ' + (error.message || error) + '</p>' +
                        '</div>');
                }

                // Initialize with empty data on error
                self.dashboardData = {
                    employee_count: 0,
                    department_count: 0,
                    new_employees: 0,
                    employees_per_department: [],
                    employees_by_gender: {
                        male: 0,
                        female: 0,
                        other: 0
                    }
                };

                return Promise.reject(error);
            });
        },

        /**
         * Render the dashboard
         * @private
         */
        _renderDashboard: function () {
            var self = this;

            // Check if we have data
            if (!this.dashboardData || Object.keys(this.dashboardData).length === 0) {
                console.error("No dashboard data available");
                this.$el.html('<div class="alert alert-warning text-center">' +
                    '<h3><i class="fa fa-exclamation-circle"></i> لا توجد بيانات متاحة</h3>' +
                    '<p>لم يتم العثور على بيانات لعرضها في لوحة المعلومات.</p>' +
                    '</div>');
                return;
            }

            // Check if there's an error
            if (this.dashboardData.error) {
                console.error("Dashboard data error:", this.dashboardData.error);
                this.$el.html('<div class="alert alert-danger text-center">' +
                    '<h3><i class="fa fa-exclamation-triangle"></i> خطأ في البيانات</h3>' +
                    '<p>' + this.dashboardData.error + '</p>' +
                    '</div>');
                return;
            }

            console.log("Rendering dashboard with data:", this.dashboardData);

            try {
                // Clear the element first
                this.$el.empty();

                // Create a container for the dashboard
                var $dashboard = $('<div class="o_hr_dashboard"></div>');
                this.$el.append($dashboard);

                // Render the template
                var html = QWeb.render('hr_dashboard_fixed_template', {
                    employee_count: this.dashboardData.employee_count,
                    department_count: this.dashboardData.department_count,
                    employees_per_department: this.dashboardData.employees_per_department,
                    new_employees: this.dashboardData.new_employees,
                    employees_by_gender: this.dashboardData.employees_by_gender,
                    round: Math.round,
                    Math: Math, // Make Math available in the template
                });

                // Append the rendered template
                $dashboard.append(html);

                console.log("Dashboard template rendered successfully");

                // Use setTimeout to ensure DOM is fully rendered before accessing canvas elements
                setTimeout(function() {
                    console.log("Rendering charts...");
                    self._renderCharts();
                }, 500); // Increased timeout to ensure DOM is fully rendered
            } catch (error) {
                console.error("Error rendering dashboard:", error);
                this.$el.html('<div class="alert alert-danger text-center">' +
                    '<h3><i class="fa fa-exclamation-triangle"></i> خطأ في عرض لوحة المعلومات</h3>' +
                    '<p>حدث خطأ أثناء عرض لوحة المعلومات. يرجى المحاولة مرة أخرى لاحقًا.</p>' +
                    '<p>التفاصيل التقنية: ' + (error.message || error) + '</p>' +
                    '</div>');
            }
        },

        /**
         * Render the charts
         * @private
         */
        _renderCharts: function () {
            try {
                console.log("Starting to render charts");

                // Check if Chart.js is available
                if (typeof Chart === 'undefined') {
                    console.error('Chart.js is not loaded');
                    return;
                }

                // Check if we have data for charts
                if (!this.dashboardData.employees_per_department ||
                    this.dashboardData.employees_per_department.length === 0) {
                    console.warn('No department data for charts');
                    this.$('#employees_per_department_chart').closest('.o_hr_dashboard_chart_container')
                        .html('<div class="alert alert-info">لا توجد بيانات للإدارات لعرضها في المخطط البياني</div>');
                } else {
                    this._renderEmployeesPerDepartmentChart();
                }

                if (!this.dashboardData.employees_by_gender) {
                    console.warn('No gender data for charts');
                    this.$('#employees_by_gender_chart').closest('.o_hr_dashboard_chart_container')
                        .html('<div class="alert alert-info">لا توجد بيانات للجنس لعرضها في المخطط البياني</div>');
                } else {
                    this._renderEmployeesByGenderChart();
                }

                console.log("Charts rendering completed");
            } catch (error) {
                console.error('Error rendering charts:', error);
                // Show error message in chart containers
                this.$('.o_hr_dashboard_chart_container').each(function() {
                    $(this).html('<div class="alert alert-danger">' +
                        '<i class="fa fa-exclamation-triangle"></i> خطأ في عرض المخطط البياني' +
                        '</div>');
                });
            }
        },

        /**
         * Render the employees per department chart
         * @private
         */
        _renderEmployeesPerDepartmentChart: function () {
            var self = this;
            var canvas = this.$('#employees_per_department_chart')[0];
            if (!canvas) {
                console.error('Canvas element for employees per department chart not found');
                return;
            }

            var ctx = canvas.getContext('2d');
            if (!ctx) {
                console.error('Could not get 2d context for employees per department chart');
                return;
            }

            // Check if we have data to display
            if (!this.dashboardData.employees_per_department || !this.dashboardData.employees_per_department.length) {
                console.warn('No department data available for chart');
                return;
            }

            var data = {
                labels: this.dashboardData.employees_per_department.map(function (dept) {
                    return dept.name;
                }),
                datasets: [{
                    data: this.dashboardData.employees_per_department.map(function (dept) {
                        return dept.employee_count;
                    }),
                    backgroundColor: this.dashboardData.employees_per_department.map(function (dept) {
                        return dept.color;
                    }),
                }]
            };

            new Chart(ctx, {
                type: 'pie',
                data: data,
                options: {
                    responsive: true,
                    legend: {
                        position: 'right',
                    },
                    title: {
                        display: false,
                    },
                    animation: {
                        animateScale: true,
                        animateRotate: true
                    }
                }
            });
        },

        /**
         * Render the employees by gender chart
         * @private
         */
        _renderEmployeesByGenderChart: function () {
            var self = this;
            var canvas = this.$('#employees_by_gender_chart')[0];
            if (!canvas) {
                console.error('Canvas element for employees by gender chart not found');
                return;
            }

            var ctx = canvas.getContext('2d');
            if (!ctx) {
                console.error('Could not get 2d context for employees by gender chart');
                return;
            }

            // Check if we have data to display
            if (!this.dashboardData.employees_by_gender) {
                console.warn('No gender data available for chart');
                return;
            }

            var data = {
                labels: ['ذكر', 'أنثى', 'أخرى'],
                datasets: [{
                    data: [
                        this.dashboardData.employees_by_gender.male || 0,
                        this.dashboardData.employees_by_gender.female || 0,
                        this.dashboardData.employees_by_gender.other || 0
                    ],
                    backgroundColor: ['#1f77b4', '#e377c2', '#7f7f7f'],
                }]
            };

            new Chart(ctx, {
                type: 'bar',
                data: data,
                options: {
                    responsive: true,
                    legend: {
                        display: false,
                    },
                    title: {
                        display: false,
                    },
                    scales: {
                        yAxes: [{
                            ticks: {
                                beginAtZero: true
                            }
                        }]
                    }
                }
            });
        },

        /**
         * Handle click on card footer
         * @private
         * @param {MouseEvent} ev
         */
        _onCardFooterClick: function (ev) {
            ev.preventDefault();
            var action = $(ev.currentTarget).data('action');
            this._openAction(action);
        },

        /**
         * Open the action
         * @private
         * @param {String} action
         */
        _openAction: function (action) {
            switch (action) {
                case 'employees':
                    this.do_action({
                        name: 'Employees',
                        res_model: 'hr.employee',
                        views: [[false, 'list'], [false, 'form']],
                        type: 'ir.actions.act_window',
                        view_mode: 'list,form',
                    });
                    break;
                case 'departments':
                    this.do_action({
                        name: 'Departments',
                        res_model: 'hr.department',
                        views: [[false, 'list'], [false, 'form']],
                        type: 'ir.actions.act_window',
                        view_mode: 'list,form',
                    });
                    break;
                case 'new_employees':
                    var date_from = moment(new Date()).subtract(30, 'days').format('YYYY-MM-DD');
                    this.do_action({
                        name: 'New Employees',
                        res_model: 'hr.employee',
                        domain: [['create_date', '>=', date_from]],
                        views: [[false, 'list'], [false, 'form']],
                        type: 'ir.actions.act_window',
                        view_mode: 'list,form',
                    });
                    break;
            }
        },
    });

    core.action_registry.add('hr_dashboard_fixed', HrDashboard);

    return HrDashboard;
});

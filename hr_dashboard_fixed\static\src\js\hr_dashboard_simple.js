odoo.define('hr_dashboard_fixed.dashboard_simple', function (require) {
    "use strict";

    var AbstractAction = require('web.AbstractAction');
    var core = require('web.core');
    var QWeb = core.qweb;
    var rpc = require('web.rpc');

    var HrDashboardSimple = AbstractAction.extend({
        template: 'hr_dashboard_fixed.dashboard',

        /**
         * @override
         */
        start: function () {
            var self = this;
            return this._super().then(function () {
                console.log("Simple Dashboard start");
                self._fetchAndRenderDashboard();
            });
        },

        _fetchAndRenderDashboard: function () {
            var self = this;
            
            // Show loading
            this.$el.html('<div class="text-center p-5">' +
                '<i class="fa fa-spinner fa-spin fa-3x"></i>' +
                '<p class="mt-2">جاري تحميل لوحة المعلومات...</p>' +
                '</div>');

            return rpc.query({
                route: '/hr_dashboard_fixed/dashboard_data',
                params: {}
            }).then(function (data) {
                console.log("Simple Dashboard data received:", data);
                self._renderSimpleDashboard(data);
            }).catch(function(error) {
                console.error("Error fetching dashboard data:", error);
                self.$el.html('<div class="alert alert-danger text-center">' +
                    '<h3><i class="fa fa-exclamation-triangle"></i> خطأ في تحميل البيانات</h3>' +
                    '<p>' + (error.message || error) + '</p>' +
                    '</div>');
            });
        },

        _renderSimpleDashboard: function (data) {
            try {
                console.log("Rendering simple dashboard with data:", data);
                
                // Clear the element
                this.$el.empty();

                // Create container
                var $dashboard = $('<div class="o_hr_dashboard"></div>');
                this.$el.append($dashboard);

                // Render simple template
                var html = QWeb.render('hr_dashboard_fixed_simple_template', {
                    employee_count: data.employee_count || 0,
                    department_count: data.department_count || 0,
                    employees_per_department: data.employees_per_department || [],
                    new_employees: data.new_employees || 0
                });

                $dashboard.append(html);
                console.log("Simple dashboard rendered successfully");
                
            } catch (error) {
                console.error("Error rendering simple dashboard:", error);
                this.$el.html('<div class="alert alert-danger text-center">' +
                    '<h3><i class="fa fa-exclamation-triangle"></i> خطأ في عرض لوحة المعلومات</h3>' +
                    '<p>' + (error.message || error) + '</p>' +
                    '</div>');
            }
        }
    });

    core.action_registry.add('hr_dashboard_fixed_simple', HrDashboardSimple);

    return HrDashboardSimple;
});

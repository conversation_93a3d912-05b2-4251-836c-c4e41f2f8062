.o_hr_dashboard {
    padding: 25px;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    min-height: 100vh;

    // Style for progress bars
    .progress {
        height: 25px;
        margin-bottom: 10px;

        .progress-bar {
            position: relative;

            .progress-bar-text {
                position: absolute;
                left: 0;
                right: 0;
                text-align: center;
                color: #000;
                font-weight: bold;
                text-shadow: 1px 1px 1px rgba(255, 255, 255, 0.5);
                line-height: 25px;
            }
        }
    }

    .o_hr_dashboard_header {
        margin-bottom: 20px;

        h1 {
            font-size: 24px;
            color: #4c4c4c;

            i {
                margin-right: 10px;
            }
        }
    }

    .o_hr_dashboard_content {
        .o_hr_dashboard_card {
            border-radius: 12px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            margin-bottom: 25px;
            overflow: hidden;
            color: white;
            transition: transform 0.3s ease, box-shadow 0.3s ease;

            &:hover {
                transform: translateY(-5px);
                box-shadow: 0 12px 35px rgba(0, 0, 0, 0.2);
            }

            .o_hr_dashboard_card_header {
                padding: 20px;
                display: flex;
                align-items: center;

                i {
                    margin-right: 20px;
                }

                .o_hr_dashboard_card_title {
                    h2 {
                        font-size: 36px;
                        margin: 0;
                        font-weight: bold;
                    }

                    p {
                        margin: 0;
                        font-size: 16px;
                    }
                }
            }

            .o_hr_dashboard_card_footer {
                padding: 10px 20px;
                background-color: rgba(0, 0, 0, 0.1);
                text-align: right;

                a {
                    color: white;

                    &:hover {
                        color: white;
                        text-decoration: none;
                    }
                }
            }
        }

        .o_hr_dashboard_chart_container, .o_hr_dashboard_table_container {
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            padding: 25px;
            margin-bottom: 25px;
            transition: box-shadow 0.3s ease;

            &:hover {
                box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
            }

            h3 {
                font-size: 20px;
                color: #2c3e50;
                margin-bottom: 25px;
                font-weight: 600;
                border-bottom: 2px solid #e9ecef;
                padding-bottom: 10px;

                i {
                    margin-right: 12px;
                    color: #007bff;
                }
            }

            .chart-container {
                position: relative;
                margin: 0 auto;

                canvas {
                    max-height: 350px !important;
                }
            }

            .o_hr_dashboard_chart {
                height: 350px;
            }

            // Enhanced table styling
            .table {
                margin-bottom: 0;
                border-radius: 8px;
                overflow: hidden;

                thead th {
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    border-top: none;
                    font-weight: 600;
                    color: white;
                    text-align: center;
                    padding: 15px 10px;
                }

                tbody tr {
                    transition: all 0.3s ease;

                    &:hover {
                        background-color: #e3f2fd;
                        transform: scale(1.02);
                    }
                }

                td {
                    vertical-align: middle;
                    padding: 12px 10px;

                    &:first-child {
                        font-weight: 500;
                    }
                }

                .badge {
                    font-size: 0.85em;
                    padding: 6px 12px;
                    border-radius: 20px;
                }
            }
        }
    }
}

# -*- coding: utf-8 -*-

import logging
from odoo import models, fields, api

_logger = logging.getLogger(__name__)

class TestDashboard(models.TransientModel):
    _name = 'test.dashboard'
    _description = 'Test Dashboard'

    def test_dashboard_data(self):
        """
        Test the dashboard data generation
        """
        try:
            dashboard = self.env['hr.dashboard']
            data = dashboard.get_dashboard_data()
            
            _logger.info("=== DASHBOARD TEST RESULTS ===")
            _logger.info("Employee count: %s", data.get('employee_count', 'NOT FOUND'))
            _logger.info("Department count: %s", data.get('department_count', 'NOT FOUND'))
            _logger.info("Employees per department: %s", data.get('employees_per_department', 'NOT FOUND'))
            _logger.info("Department statistics: %s", data.get('department_statistics', 'NOT FOUND'))
            _logger.info("New employees: %s", data.get('new_employees', 'NOT FOUND'))
            _logger.info("Employees by gender: %s", data.get('employees_by_gender', 'NOT FOUND'))
            _logger.info("=== END TEST RESULTS ===")
            
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': 'Dashboard Test',
                    'message': 'Test completed successfully. Check logs for details.',
                    'type': 'success',
                }
            }
        except Exception as e:
            _logger.error("Dashboard test failed: %s", e)
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': 'Dashboard Test Failed',
                    'message': str(e),
                    'type': 'danger',
                }
            }
